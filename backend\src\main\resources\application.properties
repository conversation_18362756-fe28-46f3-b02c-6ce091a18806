# Server Configuration
server.port=8080
server.servlet.context-path=/api

# CORS Configuration
spring.web.cors.allowed-origins=http://localhost:5173,http://localhost:3000,http://localhost:8081
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# Gemini API Configuration
gemini.api.key=AIzaSyCxM4TiYeQBGl0WfAESQ_EYtvifiV_JBgM
gemini.api.model=gemini-2.0-flash

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Logging Configuration
logging.level.com.resumeai=DEBUG
logging.level.org.springframework.web=INFO