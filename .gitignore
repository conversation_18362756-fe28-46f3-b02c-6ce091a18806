# Frontend (React/Vite) files
frontend/node_modules/
frontend/dist/
frontend/dist-ssr/
frontend/build/
frontend/.env
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Backend (Java/Maven) files
backend/target/
backend/.env

# Sensitive backend configs only - KEEP standard application.properties
backend/application-dev.properties
backend/application-prod.properties
backend/application-local.properties

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies (global patterns)
node_modules
*.local

# Build outputs (global patterns)
dist
dist-ssr
build

# Environment variables (global patterns)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
Thumbs.db
ehthumbs.db

# Java
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# Maven
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar
