
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 260 84% 74%;
    --primary-foreground: 210 40% 98%;

    --secondary: 260 38% 54%;
    --secondary-foreground: 210 40% 98%;

    --muted: 260 20% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 260 20% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 260 84% 74%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 260 84% 74%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 260 38% 54%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.text-highlight {
  @apply bg-resume-light text-resume-dark px-2 py-1 rounded-md cursor-pointer relative;
}

.resume-container {
  min-height: 400px;
}

.upload-container {
  border: 2px dashed hsl(var(--border));
  @apply rounded-lg p-8 text-center transition-all duration-200;
}

.upload-container:hover, .upload-container.active {
  border-color: hsl(var(--primary));
  @apply bg-accent/50;
}

.timer {
  @apply fixed top-4 right-4 bg-resume-primary text-white px-4 py-2 rounded-full font-medium;
}
